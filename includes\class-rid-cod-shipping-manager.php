<?php
/**
 * Multi-Country Shipping Manager
 * Handles shipping costs for multiple countries dynamically
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class RID_COD_Shipping_Manager {
    
    /**
     * Get shipping costs for a specific country and state
     *
     * @param string $country_code
     * @param string $state_code
     * @param string $delivery_type 'home' or 'desk'
     * @return float|null
     */
    public static function get_shipping_cost($country_code, $state_code, $delivery_type = 'home') {
        // Validate delivery type
        if (!in_array($delivery_type, ['home', 'desk'])) {
            $delivery_type = 'home';
        }

        // Check if states are disabled - use general shipping costs
        $show_states = get_option('rid_cod_show_states', 'yes') === 'yes';
        if (!$show_states) {
            return self::get_general_shipping_cost($delivery_type);
        }

        // Get the option name for this country
        $option_name = "rid_cod_shipping_costs_{$country_code}";
        $shipping_costs = get_option($option_name, array());

        // Check if we have costs for this specific state and delivery type
        if (isset($shipping_costs[$state_code][$delivery_type])) {
            return floatval($shipping_costs[$state_code][$delivery_type]);
        }

        // Fallback to default costs for this country
        $default_option = "rid_cod_default_shipping_{$country_code}";
        $default_costs = get_option($default_option, array());

        if (isset($default_costs[$delivery_type])) {
            return floatval($default_costs[$delivery_type]);
        }

        // Last resort: global default
        $global_default = get_option('rid_cod_global_default_shipping', array(
            'home' => 0,
            'desk' => 0
        ));

        return floatval($global_default[$delivery_type] ?? 0);
    }

    /**
     * Get general shipping cost (when states are disabled)
     *
     * @param string $delivery_type 'home' or 'desk'
     * @return float
     */
    public static function get_general_shipping_cost($delivery_type = 'home') {
        // Validate delivery type
        if (!in_array($delivery_type, ['home', 'desk'])) {
            $delivery_type = 'home';
        }

        // Check if delivery type is enabled
        $enable_delivery_type = get_option('rid_cod_enable_delivery_type', 'no') === 'yes';

        if (!$enable_delivery_type) {
            // If delivery type is not enabled, always use desk price as default
            $delivery_type = 'desk';
        }

        // Get general shipping costs
        if ($delivery_type === 'home') {
            return floatval(get_option('rid_cod_general_shipping_home', 0));
        } else {
            return floatval(get_option('rid_cod_general_shipping_desk', 0));
        }
    }

    /**
     * Set shipping cost for a specific country, state, and delivery type
     *
     * @param string $country_code
     * @param string $state_code
     * @param string $delivery_type
     * @param float $cost
     * @return bool
     */
    public static function set_shipping_cost($country_code, $state_code, $delivery_type, $cost) {
        if (!in_array($delivery_type, ['home', 'desk'])) {
            return false;
        }
        
        $option_name = "rid_cod_shipping_costs_{$country_code}";
        $shipping_costs = get_option($option_name, array());
        
        if (!isset($shipping_costs[$state_code])) {
            $shipping_costs[$state_code] = array();
        }
        
        $shipping_costs[$state_code][$delivery_type] = floatval($cost);
        
        return update_option($option_name, $shipping_costs);
    }
    
    /**
     * Get all shipping costs for a country
     *
     * @param string $country_code
     * @return array
     */
    public static function get_all_shipping_costs($country_code) {
        $option_name = "rid_cod_shipping_costs_{$country_code}";
        return get_option($option_name, array());
    }
    
    /**
     * Set default shipping costs for a country
     *
     * @param string $country_code
     * @param array $defaults ['home' => cost, 'desk' => cost]
     * @return bool
     */
    public static function set_default_shipping_costs($country_code, $defaults) {
        $option_name = "rid_cod_default_shipping_{$country_code}";
        
        $sanitized_defaults = array(
            'home' => floatval($defaults['home'] ?? 0),
            'desk' => floatval($defaults['desk'] ?? 0)
        );
        
        return update_option($option_name, $sanitized_defaults);
    }
    
    /**
     * Get default shipping costs for a country
     *
     * @param string $country_code
     * @return array
     */
    public static function get_default_shipping_costs($country_code) {
        $option_name = "rid_cod_default_shipping_{$country_code}";
        return get_option($option_name, array(
            'home' => 0,
            'desk' => 0
        ));
    }
    
    /**
     * Delete all shipping data for a country
     *
     * @param string $country_code
     * @return bool
     */
    public static function delete_country_shipping_data($country_code) {
        $option_name = "rid_cod_shipping_costs_{$country_code}";
        $default_option = "rid_cod_default_shipping_{$country_code}";
        
        delete_option($option_name);
        delete_option($default_option);
        
        return true;
    }
    
    /**
     * Import shipping costs from the old format (Algeria only)
     * This is used for migration purposes
     */
    public static function migrate_algeria_shipping_costs() {
        // Check if migration is needed
        if (get_option('rid_cod_algeria_migration_done', false)) {
            return true;
        }
        
        // Get Algeria states
        if (!class_exists('RID_COD_Country_Manager')) {
            return false;
        }
        
        $algeria_states = RID_COD_Country_Manager::get_states_by_country('DZ');
        if (empty($algeria_states)) {
            return false;
        }
        
        $migrated_costs = array();
        
        // Migrate existing costs
        foreach ($algeria_states as $state_code => $state_name) {
            $home_cost = get_option("rid_cod_shipping_cost_home_{$state_code}", 0);
            $desk_cost = get_option("rid_cod_shipping_cost_desk_{$state_code}", 0);
            
            if ($home_cost > 0 || $desk_cost > 0) {
                $migrated_costs[$state_code] = array(
                    'home' => floatval($home_cost),
                    'desk' => floatval($desk_cost)
                );
            }
        }
        
        // Save migrated costs for Algeria
        if (!empty($migrated_costs)) {
            update_option('rid_cod_shipping_costs_DZ', $migrated_costs);
        }
        
        // Set migration flag
        update_option('rid_cod_algeria_migration_done', true);
        
        return true;
    }
    
    /**
     * Get formatted shipping cost with currency
     *
     * @param string $country_code
     * @param string $state_code
     * @param string $delivery_type
     * @return string
     */
    public static function get_formatted_shipping_cost($country_code, $state_code, $delivery_type = 'home') {
        $cost = self::get_shipping_cost($country_code, $state_code, $delivery_type);
        
        if ($cost <= 0) {
            return __('توصيل مجاني', 'rid-cod');
        }
        
        // Get country currency
        $country_data = RID_COD_Country_Manager::get_country_data($country_code);
        $currency_symbol = $country_data ? $country_data['currency_symbol'] : 'د.ج';
        
        // Format number
        $formatted_cost = number_format($cost, 2, ',', '.');
        
        return $currency_symbol . ' ' . $formatted_cost;
    }
    
    /**
     * Check if shipping is available for a country/state combination
     *
     * @param string $country_code
     * @param string $state_code
     * @return bool
     */
    public static function is_shipping_available($country_code, $state_code) {
        $home_cost = self::get_shipping_cost($country_code, $state_code, 'home');
        $desk_cost = self::get_shipping_cost($country_code, $state_code, 'desk');
        
        // Shipping is available if we have any non-negative cost (0 means free shipping)
        return ($home_cost >= 0 || $desk_cost >= 0);
    }
}
